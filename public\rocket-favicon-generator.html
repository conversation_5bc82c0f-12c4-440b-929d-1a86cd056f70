<!DOCTYPE html>
<html>
<head>
    <title>🚀 Rocket Favicon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; text-align: center; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        canvas { border: 2px solid #ddd; margin: 10px; }
        button { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px 5px; }
        button:hover { opacity: 0.9; }
        .preview { display: flex; justify-content: center; gap: 20px; margin: 20px 0; flex-wrap: wrap; }
        .size-preview { text-align: center; }
        h1 { color: #333; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Rocket Favicon Generator</h1>
        
        <div class="info">
            <strong>Perfect!</strong> This will generate rocket favicons in different sizes for your portfolio.
        </div>

        <div class="preview">
            <div class="size-preview">
                <div>16x16</div>
                <canvas id="canvas16" width="16" height="16" style="transform: scale(3);"></canvas>
            </div>
            <div class="size-preview">
                <div>32x32</div>
                <canvas id="canvas32" width="32" height="32" style="transform: scale(2);"></canvas>
            </div>
            <div class="size-preview">
                <div>48x48</div>
                <canvas id="canvas48" width="48" height="48" style="transform: scale(1.5);"></canvas>
            </div>
        </div>

        <div>
            <button onclick="downloadFavicon(16)">Download 16x16 PNG</button>
            <button onclick="downloadFavicon(32)">Download 32x32 PNG</button>
            <button onclick="downloadFavicon(48)">Download 48x48 PNG</button>
            <button onclick="downloadAll()">Download All</button>
        </div>

        <div class="info">
            <strong>Next Steps:</strong>
            <ol>
                <li>Download the 32x32 PNG and save it as <code>favicon.png</code> in your public folder</li>
                <li>Download the 16x16 PNG and save it as <code>favicon-16x16.png</code> in your public folder</li>
                <li>Run <code>npm run build && npm run deploy</code> to update your website</li>
                <li>Your browser tab will show a cool rocket icon! 🚀</li>
            </ol>
        </div>
    </div>

    <script>
        function drawRocket(ctx, size) {
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Scale factor
            const scale = size / 32;
            
            // Background gradient
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#667eea');
            bgGradient.addColorStop(1, '#764ba2');
            
            // Draw background
            ctx.fillStyle = bgGradient;
            ctx.roundRect(0, 0, size, size, 6 * scale);
            ctx.fill();
            
            // Rocket body
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.roundRect(13 * scale, 4 * scale, 6 * scale, 18 * scale, 3 * scale);
            ctx.fill();
            
            // Rocket nose
            ctx.fillStyle = '#f0f0f0';
            ctx.beginPath();
            ctx.roundRect(14 * scale, 4 * scale, 4 * scale, 4 * scale, 2 * scale);
            ctx.fill();
            
            // Rocket window
            const windowGradient = ctx.createLinearGradient(0, 0, size, size);
            windowGradient.addColorStop(0, '#87ceeb');
            windowGradient.addColorStop(1, '#4682b4');
            ctx.fillStyle = windowGradient;
            ctx.beginPath();
            ctx.arc(16 * scale, 12 * scale, 2 * scale, 0, Math.PI * 2);
            ctx.fill();
            
            // Rocket fins
            ctx.fillStyle = '#e0e0e0';
            ctx.beginPath();
            ctx.moveTo(13 * scale, 18 * scale);
            ctx.lineTo(11 * scale, 22 * scale);
            ctx.lineTo(13 * scale, 20 * scale);
            ctx.fill();
            
            ctx.beginPath();
            ctx.moveTo(19 * scale, 18 * scale);
            ctx.lineTo(21 * scale, 22 * scale);
            ctx.lineTo(19 * scale, 20 * scale);
            ctx.fill();
            
            // Rocket flames
            const flameGradient = ctx.createLinearGradient(0, 22 * scale, 0, 28 * scale);
            flameGradient.addColorStop(0, '#ff6b35');
            flameGradient.addColorStop(0.5, '#f7931e');
            flameGradient.addColorStop(1, '#ffd700');
            ctx.fillStyle = flameGradient;
            ctx.beginPath();
            ctx.moveTo(14 * scale, 22 * scale);
            ctx.lineTo(16 * scale, 28 * scale);
            ctx.lineTo(18 * scale, 22 * scale);
            ctx.lineTo(16 * scale, 24 * scale);
            ctx.fill();
        }
        
        // Generate all sizes
        [16, 32, 48].forEach(size => {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Polyfill for roundRect if not supported
            if (!ctx.roundRect) {
                ctx.roundRect = function(x, y, width, height, radius) {
                    this.beginPath();
                    this.moveTo(x + radius, y);
                    this.lineTo(x + width - radius, y);
                    this.quadraticCurveTo(x + width, y, x + width, y + radius);
                    this.lineTo(x + width, y + height - radius);
                    this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                    this.lineTo(x + radius, y + height);
                    this.quadraticCurveTo(x, y + height, x, y + height - radius);
                    this.lineTo(x, y + radius);
                    this.quadraticCurveTo(x, y, x + radius, y);
                    this.closePath();
                };
            }
            
            drawRocket(ctx, size);
        });
        
        function downloadFavicon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = size === 32 ? 'favicon.png' : `favicon-${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadAll() {
            setTimeout(() => downloadFavicon(16), 100);
            setTimeout(() => downloadFavicon(32), 200);
            setTimeout(() => downloadFavicon(48), 300);
        }
    </script>
</body>
</html>
