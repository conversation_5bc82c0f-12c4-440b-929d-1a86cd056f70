.education {
  position: relative;
  background: #000000;
}

.education::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.education-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  transform: translateX(-50%);
  z-index: 1;
}

.education-item {
  position: relative;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
  z-index: 2;
}

.education-item:nth-child(odd) {
  justify-content: flex-start;
}

.education-item:nth-child(even) {
  justify-content: flex-end;
}

.education-item:nth-child(odd) .education-card {
  margin-right: 2rem;
  text-align: right;
}

.education-item:nth-child(even) .education-card {
  margin-left: 2rem;
  text-align: left;
}

.education-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  max-width: 350px;
  position: relative;
  transition: all 0.3s ease;
}

.education-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
}

.education-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.education-degree {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.3;
}

.education-duration {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  display: inline-block;
  width: fit-content;
}

.education-institution {
  font-size: 1.1rem;
  font-weight: 600;
  color: #b3b3b3;
  margin-bottom: 0.5rem;
}

.education-location {
  font-size: 0.95rem;
  color: #888888;
  margin-bottom: 1rem;
}

.education-grade {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.grade-label {
  font-size: 0.9rem;
  color: #b3b3b3;
  font-weight: 500;
}

.grade-value {
  font-size: 1rem;
  color: #667eea;
  font-weight: 600;
}

.timeline-dot {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  border: 3px solid #000000;
  z-index: 3;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .timeline-line {
    left: 30px;
  }
  
  .education-item {
    justify-content: flex-start !important;
    padding-left: 60px;
  }
  
  .education-item:nth-child(odd) .education-card,
  .education-item:nth-child(even) .education-card {
    margin: 0;
    text-align: left;
    max-width: 100%;
  }
  
  .timeline-dot {
    left: 30px;
  }
  
  .education-card {
    padding: 1.5rem;
  }
  
  .education-degree {
    font-size: 1.2rem;
  }
  
  .education-institution {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .education-item {
    padding-left: 50px;
  }
  
  .timeline-line {
    left: 25px;
  }
  
  .timeline-dot {
    left: 25px;
    width: 12px;
    height: 12px;
  }
  
  .education-card {
    padding: 1.2rem;
  }
  
  .education-degree {
    font-size: 1.1rem;
  }
  
  .education-header {
    gap: 0.3rem;
  }
  
  .education-duration {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
  }
}
