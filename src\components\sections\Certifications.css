.certifications {
  padding: 6rem 0;
  background: #000000;
  position: relative;
  overflow: hidden;
}

.certifications::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.certifications .container {
  position: relative;
  z-index: 2;
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.certification-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-height: 140px;
}

.certification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.certification-card:hover::before {
  opacity: 1;
}

.certification-card:hover {
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 0 15px 50px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.cert-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.icon-emoji {
  font-size: 1.8rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.cert-content {
  flex: 1;
}

.cert-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.cert-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.cert-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  border: 1px solid rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.cert-link:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #ffffff;
  border-color: #667eea;
}

.cert-link svg {
  font-size: 1rem;
}

.cert-issuer {
  font-size: 1rem;
  color: #667eea;
  font-weight: 500;
  margin: 0 0 0.8rem 0;
}

.cert-description {
  font-size: 0.9rem;
  color: #b3b3b3;
  line-height: 1.5;
  margin: 0;
  font-weight: 400;
}

/* Responsive Design */
@media (max-width: 768px) {
  .certifications {
    padding: 4rem 0;
  }
  
  .certifications-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
  }
  
  .certification-card {
    padding: 1.5rem;
    gap: 1rem;
    min-height: 120px;
  }

  .cert-icon {
    width: 50px;
    height: 50px;
  }

  .icon-emoji {
    font-size: 1.5rem;
  }

  .cert-title {
    font-size: 1.1rem;
  }

  .cert-issuer {
    font-size: 0.9rem;
    margin-bottom: 0.6rem;
  }

  .cert-description {
    font-size: 0.85rem;
  }

  .cert-links {
    flex-direction: column;
    gap: 0.8rem;
  }

  .cert-link {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .certifications {
    padding: 3rem 0;
  }
  
  .certification-card {
    padding: 1.2rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    min-height: auto;
  }

  .cert-icon {
    width: 45px;
    height: 45px;
    margin: 0 auto;
  }

  .icon-emoji {
    font-size: 1.3rem;
  }

  .cert-title {
    font-size: 1rem;
  }

  .cert-issuer {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .cert-description {
    font-size: 0.8rem;
  }
}
