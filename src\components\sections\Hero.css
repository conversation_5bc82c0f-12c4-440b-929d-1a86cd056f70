.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.orb-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.hero-text {
  z-index: 2;
}

.hero-name {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #b3b3b3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #667eea;
  font-weight: 600;
  min-height: 2.5rem;
}

.typing-text {
  display: inline-block;
}

.cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: #667eea;
  font-weight: 300;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  color: #b3b3b3;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.hero-social {
  display: flex;
  gap: 1.5rem;
  justify-content: flex-start;
  align-items: center;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #b3b3b3;
  font-size: 1.5rem;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.social-link:hover {
  color: white;
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.social-link:hover::before {
  opacity: 1;
}

.hero-avatar {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 2;
  height: 100%;
}

.avatar-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  z-index: 2;
  border: 3px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  background: transparent;
}

.avatar-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 3;
}

.profile-photo {
  transition: transform 0.3s ease, filter 0.3s ease;
  filter: brightness(1.1) contrast(1.1) saturate(1.05);
}

.profile-photo:hover {
  transform: scale(1.05);
  filter: brightness(0.95) contrast(1.25) saturate(1.15);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.avatar-initials {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.avatar-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.3;
  filter: blur(20px);
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding-top: 100px;
    text-align: center;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .hero-name {
    font-size: 2.5rem;
  }
  
  .hero-title {
    font-size: 1.5rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
    max-width: 100%;
  }
  
  .hero-buttons {
    justify-content: center;
  }

  .hero-social {
    justify-content: center;
  }
  
  .avatar-container {
    width: 250px;
    height: 250px;
  }

  .avatar-initials {
    font-size: 3rem;
  }
  
  .gradient-orb {
    filter: blur(80px);
  }
  
  .orb-1 {
    width: 200px;
    height: 200px;
  }
  
  .orb-2 {
    width: 150px;
    height: 150px;
  }
  
  .orb-3 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .hero-name {
    font-size: 2rem;
  }
  
  .hero-title {
    font-size: 1.3rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 250px;
  }
  
  .profile-image-container {
    width: 250px;
    height: 320px;
  }

  .profile-image-wrapper {
    padding: 12px;
    border-radius: 16px;
  }

  .profile-image-full {
    border-radius: 10px;
  }

  .profile-initials {
    font-size: 3rem;
  }
}
