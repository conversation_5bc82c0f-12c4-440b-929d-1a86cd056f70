<!DOCTYPE html>
<html>
<head>
    <title>Profile Photo to Favicon Converter</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        canvas { border: 2px solid #ddd; margin: 10px; border-radius: 50%; }
        button { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px 5px; }
        button:hover { opacity: 0.9; }
        .preview { display: flex; align-items: center; gap: 20px; margin: 20px 0; }
        .sizes { display: flex; gap: 10px; flex-wrap: wrap; }
        h1 { color: #333; text-align: center; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Profile Photo to Favicon Converter</h1>
        
        <div class="info">
            <strong>Instructions:</strong>
            <ol>
                <li>Upload your own image or use the default profile photo</li>
                <li>Preview different favicon sizes below</li>
                <li>Download the sizes you need</li>
                <li>Replace the existing favicon files in your public folder</li>
            </ol>
        </div>

        <div style="margin: 20px 0; text-align: center;">
            <input type="file" id="imageUpload" accept="image/*" style="margin: 10px;">
            <button onclick="useCustomImage()">Use My Custom Image</button>
            <button onclick="useProfilePhoto()">Use Profile Photo</button>
            <button onclick="useInitials()">Use Initials (RK)</button>
        </div>

        <div class="preview">
            <div>
                <h3>Original Photo:</h3>
                <img id="originalImg" style="width: 150px; height: 150px; object-fit: cover; border-radius: 50%; border: 3px solid #667eea;">
            </div>
            <div>
                <h3>Favicon Previews:</h3>
                <div class="sizes">
                    <div style="text-align: center;">
                        <div>16x16</div>
                        <canvas id="canvas16" width="16" height="16" style="transform: scale(2);"></canvas>
                    </div>
                    <div style="text-align: center;">
                        <div>32x32</div>
                        <canvas id="canvas32" width="32" height="32" style="transform: scale(2);"></canvas>
                    </div>
                    <div style="text-align: center;">
                        <div>48x48</div>
                        <canvas id="canvas48" width="48" height="48" style="transform: scale(1.5);"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="downloadFavicon(16)">Download 16x16 PNG</button>
            <button onclick="downloadFavicon(32)">Download 32x32 PNG</button>
            <button onclick="downloadFavicon(48)">Download 48x48 PNG</button>
            <button onclick="downloadICO()">Download ICO File</button>
        </div>

        <div class="info">
            <strong>Next Steps:</strong>
            <ol>
                <li>Download the 32x32 PNG and save it as <code>favicon.png</code> in your public folder</li>
                <li>Download the 16x16 PNG and save it as <code>favicon-16x16.png</code> in your public folder</li>
                <li>Run <code>npm run build && npm run deploy</code> to update your website</li>
            </ol>
        </div>
    </div>

    <script>
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        // Load the profile photo
        img.onload = function() {
            document.getElementById('originalImg').src = img.src;
            generateFavicons();
        };
        
        img.onerror = function() {
            alert('Could not load profile photo. Make sure profile-photo.jpg exists in the images folder.');
        };
        
        // Try to load the custom image first, fallback to profile photo
        img.src = './images/favicon-source.jpg';
        img.onerror = function() {
            console.log('Custom image not found, trying profile photo...');
            img.src = './images/profile-photo.jpg';
        };

        // Handle file upload
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        function useCustomImage() {
            img.src = './images/favicon-source.jpg';
        }

        function useProfilePhoto() {
            img.src = './images/profile-photo.jpg';
        }

        function useInitials() {
            // Create a canvas with initials
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 200;
            tempCanvas.height = 200;
            const tempCtx = tempCanvas.getContext('2d');

            // Create gradient background
            const gradient = tempCtx.createLinearGradient(0, 0, 200, 200);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');

            // Draw background
            tempCtx.fillStyle = gradient;
            tempCtx.fillRect(0, 0, 200, 200);

            // Draw text
            tempCtx.fillStyle = 'white';
            tempCtx.font = 'bold 80px Inter, Arial, sans-serif';
            tempCtx.textAlign = 'center';
            tempCtx.textBaseline = 'middle';
            tempCtx.fillText('RK', 100, 100);

            img.src = tempCanvas.toDataURL();
        }
        
        function generateFavicons() {
            [16, 32, 48].forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // Clear canvas
                ctx.clearRect(0, 0, size, size);
                
                // Draw circular clipped image
                ctx.save();
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                ctx.clip();
                
                // Draw the image
                ctx.drawImage(img, 0, 0, size, size);
                ctx.restore();
                
                // Add a subtle border
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2 - 0.5, 0, Math.PI * 2);
                ctx.stroke();
            });
        }
        
        function downloadFavicon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = size === 32 ? 'favicon.png' : `favicon-${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadICO() {
            // For ICO, we'll use the 32x32 version
            const canvas = document.getElementById('canvas32');
            const link = document.createElement('a');
            link.download = 'favicon.ico';
            link.href = canvas.toDataURL('image/png');
            link.click();
            alert('Note: This downloads as PNG. For true ICO format, use an online converter or image editor.');
        }
    </script>
</body>
</html>
