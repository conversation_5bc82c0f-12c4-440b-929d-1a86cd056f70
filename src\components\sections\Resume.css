.resume {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(0, 0, 0, 0.9) 100%);
  position: relative;
}

.resume::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.resume-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.resume-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem;
  margin: 3rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.resume-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.resume-card:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.resume-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.resume-text {
  text-align: center;
  max-width: 500px;
}

.resume-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.resume-description {
  font-size: 1.1rem;
  color: #b3b3b3;
  line-height: 1.7;
}

.resume-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  justify-content: center;
  min-width: 180px;
}

.download-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.download-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.download-btn.secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
}

.download-btn.secondary:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.download-btn svg {
  font-size: 1.2rem;
}

.resume-preview {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
}

.preview-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
  text-align: center;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.preview-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.preview-label {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.preview-value {
  font-size: 1rem;
  color: #ffffff;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .resume-card {
    padding: 2rem;
    margin: 2rem 0;
  }
  
  .resume-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .resume-title {
    font-size: 1.5rem;
  }
  
  .resume-description {
    font-size: 1rem;
  }
  
  .resume-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .download-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    min-width: 200px;
  }
  
  .preview-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .preview-item {
    padding: 0.8rem;
  }
}

@media (max-width: 480px) {
  .resume-card {
    padding: 1.5rem;
  }
  
  .resume-title {
    font-size: 1.3rem;
  }
  
  .resume-description {
    font-size: 0.95rem;
  }
  
  .download-btn {
    padding: 0.7rem 1.2rem;
    font-size: 0.95rem;
    min-width: 180px;
  }
  
  .preview-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-title {
    font-size: 1.2rem;
  }
}
