<svg width="400" height="250" viewBox="0 0 400 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="250" fill="#1a1a2e"/>
  <rect x="20" y="20" width="360" height="210" rx="8" fill="#16213e" stroke="#0f3460" stroke-width="2"/>
  
  <!-- Header -->
  <rect x="40" y="40" width="320" height="30" rx="4" fill="#0f3460"/>
  <text x="200" y="60" text-anchor="middle" fill="#e94560" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Deepfake Detection</text>
  
  <!-- Neural network visualization -->
  <!-- Input layer -->
  <circle cx="80" cy="100" r="8" fill="#e94560"/>
  <circle cx="80" cy="130" r="8" fill="#e94560"/>
  <circle cx="80" cy="160" r="8" fill="#e94560"/>
  
  <!-- Hidden layer 1 -->
  <circle cx="150" cy="90" r="8" fill="#0f3460"/>
  <circle cx="150" cy="115" r="8" fill="#0f3460"/>
  <circle cx="150" cy="140" r="8" fill="#0f3460"/>
  <circle cx="150" cy="165" r="8" fill="#0f3460"/>
  
  <!-- Hidden layer 2 -->
  <circle cx="220" cy="100" r="8" fill="#0f3460"/>
  <circle cx="220" cy="125" r="8" fill="#0f3460"/>
  <circle cx="220" cy="150" r="8" fill="#0f3460"/>
  
  <!-- Output layer -->
  <circle cx="290" cy="115" r="8" fill="#e94560"/>
  <circle cx="290" cy="140" r="8" fill="#e94560"/>
  
  <!-- Connections -->
  <line x1="88" y1="100" x2="142" y2="90" stroke="#0f3460" stroke-width="1"/>
  <line x1="88" y1="130" x2="142" y2="115" stroke="#0f3460" stroke-width="1"/>
  <line x1="88" y1="160" x2="142" y2="140" stroke="#0f3460" stroke-width="1"/>
  
  <line x1="158" y1="90" x2="212" y2="100" stroke="#0f3460" stroke-width="1"/>
  <line x1="158" y1="115" x2="212" y2="125" stroke="#0f3460" stroke-width="1"/>
  <line x1="158" y1="140" x2="212" y2="150" stroke="#0f3460" stroke-width="1"/>
  
  <line x1="228" y1="100" x2="282" y2="115" stroke="#0f3460" stroke-width="1"/>
  <line x1="228" y1="125" x2="282" y2="140" stroke="#0f3460" stroke-width="1"/>
  
  <!-- CNN/LSTM labels -->
  <text x="200" y="200" text-anchor="middle" fill="#e94560" font-family="Arial, sans-serif" font-size="12" font-weight="bold">CNN + LSTM</text>
</svg>
