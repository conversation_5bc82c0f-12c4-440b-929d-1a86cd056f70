<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="32" height="32" rx="6" fill="url(#gradient)"/>

  <!-- RK Text -->
  <text x="16" y="22" font-family="Inter, Arial, sans-serif" font-size="14" font-weight="700" text-anchor="middle" fill="white">RK</text>

  <!-- Subtle glow effect -->
  <text x="16" y="22" font-family="Inter, Arial, sans-serif" font-size="14" font-weight="700" text-anchor="middle" fill="rgba(255,255,255,0.3)" filter="blur(1px)">RK</text>

  <defs>
    <!-- Background Gradient -->
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
