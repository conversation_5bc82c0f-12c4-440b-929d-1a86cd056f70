.projects {
  background: #000000;
  position: relative;
}

.projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.filter-menu {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 3rem 0;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid rgba(102, 126, 234, 0.3);
  background: transparent;
  color: #b3b3b3;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(102, 126, 234, 0.3);
}

.project-card.coming-soon {
  opacity: 0.7;
  border: 2px dashed rgba(102, 126, 234, 0.5);
}

.project-card.coming-soon:hover {
  opacity: 0.9;
  border-color: #667eea;
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.project-icon {
  font-size: 3rem;
  opacity: 0.7;
}

.coming-soon-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
}

.coming-soon-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #667eea;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.project-content {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.8rem;
}

.project-description {
  color: #b3b3b3;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  font-size: 0.95rem;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-badge {
  padding: 0.3rem 0.8rem;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.project-links {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.8rem;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.85rem;
  border: 1px solid rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  flex: 1;
  min-width: fit-content;
  justify-content: center;
}

.project-link:hover {
  background: rgba(102, 126, 234, 0.2);
  color: #ffffff;
  border-color: #667eea;
}

.project-link svg {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .filter-menu {
    gap: 0.8rem;
    margin: 2rem 0;
  }
  
  .filter-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
  
  .project-content {
    padding: 1.2rem;
  }
  
  .project-title {
    font-size: 1.2rem;
  }
  
  .project-description {
    font-size: 0.9rem;
  }
  
  .project-links {
    flex-direction: row;
    gap: 0.4rem;
    flex-wrap: wrap;
  }

  .project-link {
    justify-content: center;
    flex: 1;
    min-width: calc(50% - 0.2rem);
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-menu {
    flex-direction: column;
    align-items: center;
    gap: 0.6rem;
  }
  
  .filter-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .project-image {
    height: 150px;
  }
  
  .project-icon {
    font-size: 2rem;
  }
  
  .coming-soon-text {
    font-size: 1.2rem;
  }
  
  .project-content {
    padding: 1rem;
  }
  
  .project-title {
    font-size: 1.1rem;
  }
}
