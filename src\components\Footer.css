.footer {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 20, 0.95) 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 0 2rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer-content {
  position: relative;
  z-index: 2;
}

.footer-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.footer-brand {
  text-align: left;
}

.footer-name {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.footer-title {
  font-size: 0.9rem;
  color: #b3b3b3;
  font-weight: 500;
}

.footer-social {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.social-link {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #b3b3b3;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--hover-color, #667eea);
  border-color: var(--hover-color, #667eea);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.social-tooltip {
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 0.8rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 10;
}

.social-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.social-link:hover .social-tooltip {
  opacity: 1;
  bottom: 130%;
}

.footer-bottom {
  text-align: center;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
  margin-bottom: 1.5rem;
}

.footer-copyright {
  font-size: 0.9rem;
  color: #888888;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.heart-icon {
  color: #ff6b6b;
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1.5rem;
  }
  
  .footer-main {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-brand {
    text-align: center;
  }
  
  .footer-name {
    font-size: 1.3rem;
  }
  
  .footer-title {
    font-size: 0.85rem;
  }
  
  .footer-social {
    gap: 1.2rem;
  }
  
  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
  
  .footer-copyright {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 1.5rem 0 1rem;
  }
  
  .footer-main {
    gap: 1.5rem;
  }
  
  .footer-name {
    font-size: 1.2rem;
  }
  
  .footer-title {
    font-size: 0.8rem;
  }
  
  .footer-social {
    gap: 1rem;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .footer-copyright {
    font-size: 0.8rem;
    line-height: 1.5;
  }
}
