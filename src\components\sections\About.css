.about {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.9) 100%);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #ffffff;
}

.about-content-centered {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-text {
  z-index: 2;
}

.about-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  color: #b3b3b3;
}

.about-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.detail-label {
  font-weight: 600;
  color: #667eea;
  min-width: 80px;
}

.detail-value {
  color: #ffffff;
  font-weight: 500;
}



/* Responsive Design */
@media (max-width: 768px) {
  .about-content-centered {
    max-width: 600px;
    padding: 0 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-description {
    font-size: 1.1rem;
  }

  .about-details {
    max-width: 500px;
  }
}

@media (max-width: 480px) {
  .about-content-centered {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .about-description {
    font-size: 1rem;
  }

  .detail-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .detail-label {
    min-width: auto;
  }

  .about-details {
    max-width: 100%;
  }
}
