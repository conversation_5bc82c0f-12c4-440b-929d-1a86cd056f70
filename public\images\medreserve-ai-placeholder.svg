<svg width="400" height="250" viewBox="0 0 400 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="250" fill="#1a1a2e"/>
  <rect x="20" y="20" width="360" height="210" rx="8" fill="#16213e" stroke="#0f3460" stroke-width="2"/>
  
  <!-- Header -->
  <rect x="40" y="40" width="320" height="30" rx="4" fill="#0f3460"/>
  <text x="200" y="60" text-anchor="middle" fill="#e94560" font-family="Arial, sans-serif" font-size="14" font-weight="bold">MedReserve AI</text>
  
  <!-- Medical cross icon -->
  <rect x="80" y="90" width="40" height="8" fill="#e94560" rx="4"/>
  <rect x="96" y="74" width="8" height="40" fill="#e94560" rx="4"/>
  
  <!-- Dashboard elements -->
  <rect x="140" y="85" width="60" height="6" fill="#0f3460" rx="3"/>
  <rect x="140" y="95" width="80" height="6" fill="#0f3460" rx="3"/>
  <rect x="140" y="105" width="50" height="6" fill="#0f3460" rx="3"/>
  
  <!-- Calendar/booking icon -->
  <rect x="250" y="80" width="50" height="40" fill="none" stroke="#e94560" stroke-width="2" rx="4"/>
  <line x1="260" y1="75" x2="260" y2="85" stroke="#e94560" stroke-width="2"/>
  <line x1="270" y1="75" x2="270" y2="85" stroke="#e94560" stroke-width="2"/>
  <line x1="280" y1="75" x2="280" y2="85" stroke="#e94560" stroke-width="2"/>
  <line x1="290" y1="75" x2="290" y2="85" stroke="#e94560" stroke-width="2"/>
  
  <!-- Grid lines in calendar -->
  <line x1="255" y1="95" x2="295" y2="95" stroke="#0f3460" stroke-width="1"/>
  <line x1="255" y1="105" x2="295" y2="105" stroke="#0f3460" stroke-width="1"/>
  <line x1="265" y1="90" x2="265" y2="115" stroke="#0f3460" stroke-width="1"/>
  <line x1="275" y1="90" x2="275" y2="115" stroke="#0f3460" stroke-width="1"/>
  <line x1="285" y1="90" x2="285" y2="115" stroke="#0f3460" stroke-width="1"/>
  
  <!-- AI brain icon -->
  <circle cx="320" cy="100" r="15" fill="none" stroke="#e94560" stroke-width="2"/>
  <path d="M310 100 Q315 90 320 100 Q325 90 330 100" stroke="#e94560" stroke-width="2" fill="none"/>
  <path d="M310 100 Q315 110 320 100 Q325 110 330 100" stroke="#e94560" stroke-width="2" fill="none"/>
  <circle cx="315" cy="95" r="1.5" fill="#e94560"/>
  <circle cx="325" cy="95" r="1.5" fill="#e94560"/>
  <circle cx="315" cy="105" r="1.5" fill="#e94560"/>
  <circle cx="325" cy="105" r="1.5" fill="#e94560"/>
  
  <!-- Chat bubbles -->
  <ellipse cx="100" cy="150" rx="25" ry="15" fill="#0f3460"/>
  <ellipse cx="150" cy="140" rx="30" ry="18" fill="#e94560"/>
  <ellipse cx="200" cy="155" rx="28" ry="16" fill="#0f3460"/>
  
  <!-- WebSocket connection lines -->
  <path d="M125 150 Q137 145 150 140" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
  <path d="M180 140 Q190 147 200 155" stroke="#667eea" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
  
  <!-- Tech stack indicators -->
  <text x="60" y="190" fill="#667eea" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Spring Boot</text>
  <text x="140" y="190" fill="#667eea" font-family="Arial, sans-serif" font-size="10" font-weight="bold">React.js</text>
  <text x="200" y="190" fill="#667eea" font-family="Arial, sans-serif" font-size="10" font-weight="bold">MySQL</text>
  <text x="250" y="190" fill="#667eea" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Docker</text>
  <text x="300" y="190" fill="#667eea" font-family="Arial, sans-serif" font-size="10" font-weight="bold">FastAPI</text>
  
  <!-- Security/JWT indicator -->
  <rect x="320" y="130" width="20" height="15" fill="none" stroke="#e94560" stroke-width="1.5" rx="2"/>
  <circle cx="330" cy="125" r="3" fill="none" stroke="#e94560" stroke-width="1.5"/>
  <text x="310" y="155" fill="#e94560" font-family="Arial, sans-serif" font-size="8">JWT</text>
</svg>
