<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" fill="url(#bgGradient)"/>
  
  <!-- Main Container -->
  <rect x="20" y="20" width="360" height="210" rx="12" fill="url(#cardGradient)" stroke="rgba(102, 126, 234, 0.3)" stroke-width="1"/>
  
  <!-- Header Section -->
  <rect x="40" y="40" width="320" height="50" rx="8" fill="rgba(102, 126, 234, 0.05)" stroke="rgba(102, 126, 234, 0.2)" stroke-width="1"/>
  
  <!-- AI Brain Icon -->
  <circle cx="70" cy="65" r="15" fill="url(#iconGradient)"/>
  <path d="M62 58 Q70 50 78 58 Q78 65 78 72 Q70 80 62 72 Q62 65 62 58 Z" fill="white" opacity="0.9"/>
  <circle cx="66" cy="62" r="2" fill="url(#iconGradient)"/>
  <circle cx="74" cy="62" r="2" fill="url(#iconGradient)"/>
  <circle cx="70" cy="68" r="1.5" fill="url(#iconGradient)"/>
  
  <!-- Title -->
  <text x="100" y="60" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">Smart Summary & Q&A</text>
  <text x="100" y="78" font-family="Arial, sans-serif" font-size="12" fill="#a0a0a0">AI-Powered Platform</text>
  
  <!-- Feature Cards -->
  <!-- Video Processing -->
  <rect x="40" y="110" width="100" height="60" rx="6" fill="rgba(102, 126, 234, 0.08)" stroke="rgba(102, 126, 234, 0.2)" stroke-width="1"/>
  <rect x="50" y="120" width="20" height="15" rx="2" fill="url(#iconGradient)"/>
  <circle cx="60" cy="127.5" r="3" fill="white"/>
  <polygon points="58,125 58,130 62,127.5" fill="url(#iconGradient)"/>
  <text x="50" y="145" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#ffffff">Video</text>
  <text x="50" y="157" font-family="Arial, sans-serif" font-size="8" fill="#a0a0a0">Processing</text>
  
  <!-- Document Analysis -->
  <rect x="150" y="110" width="100" height="60" rx="6" fill="rgba(102, 126, 234, 0.08)" stroke="rgba(102, 126, 234, 0.2)" stroke-width="1"/>
  <rect x="160" y="120" width="16" height="20" rx="2" fill="url(#iconGradient)"/>
  <line x1="165" y1="125" x2="170" y2="125" stroke="white" stroke-width="1"/>
  <line x1="165" y1="128" x2="170" y2="128" stroke="white" stroke-width="1"/>
  <line x1="165" y1="131" x2="168" y2="131" stroke="white" stroke-width="1"/>
  <text x="160" y="145" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#ffffff">Document</text>
  <text x="160" y="157" font-family="Arial, sans-serif" font-size="8" fill="#a0a0a0">Analysis</text>
  
  <!-- Q&A System -->
  <rect x="260" y="110" width="100" height="60" rx="6" fill="rgba(102, 126, 234, 0.08)" stroke="rgba(102, 126, 234, 0.2)" stroke-width="1"/>
  <circle cx="280" cy="130" r="8" fill="url(#iconGradient)"/>
  <text x="276" y="135" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">?</text>
  <rect x="295" y="125" width="15" height="8" rx="2" fill="rgba(102, 126, 234, 0.3)"/>
  <text x="270" y="145" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#ffffff">Q&A</text>
  <text x="270" y="157" font-family="Arial, sans-serif" font-size="8" fill="#a0a0a0">Interactive</text>
  
  <!-- Tech Stack Icons -->
  <text x="40" y="195" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#667eea">Tech Stack:</text>
  
  <!-- React -->
  <circle cx="120" cy="190" r="8" fill="rgba(97, 218, 251, 0.2)" stroke="#61dafb" stroke-width="1"/>
  <text x="116" y="195" font-family="Arial, sans-serif" font-size="8" fill="#61dafb">R</text>
  
  <!-- Node.js -->
  <circle cx="140" cy="190" r="8" fill="rgba(104, 160, 99, 0.2)" stroke="#68a063" stroke-width="1"/>
  <text x="136" y="195" font-family="Arial, sans-serif" font-size="8" fill="#68a063">N</text>
  
  <!-- AI -->
  <circle cx="160" cy="190" r="8" fill="rgba(102, 126, 234, 0.2)" stroke="#667eea" stroke-width="1"/>
  <text x="156" y="195" font-family="Arial, sans-serif" font-size="8" fill="#667eea">AI</text>
  
  <!-- Python -->
  <circle cx="180" cy="190" r="8" fill="rgba(255, 212, 59, 0.2)" stroke="#ffd43b" stroke-width="1"/>
  <text x="176" y="195" font-family="Arial, sans-serif" font-size="8" fill="#ffd43b">P</text>
  
  <!-- Decorative Elements -->
  <circle cx="350" cy="50" r="2" fill="rgba(102, 126, 234, 0.3)"/>
  <circle cx="360" cy="60" r="1.5" fill="rgba(102, 126, 234, 0.2)"/>
  <circle cx="340" cy="70" r="1" fill="rgba(102, 126, 234, 0.4)"/>
  
  <!-- Data Flow Lines -->
  <path d="M90 65 Q200 65 310 65" stroke="rgba(102, 126, 234, 0.3)" stroke-width="1" fill="none" stroke-dasharray="3,3"/>
  <path d="M90 140 Q150 140 210 140" stroke="rgba(102, 126, 234, 0.2)" stroke-width="1" fill="none" stroke-dasharray="2,2"/>
</svg>
